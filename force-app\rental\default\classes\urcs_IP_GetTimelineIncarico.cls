/**
 * @File Name         : urcs_IP_GetTimelineIncarico.cls
 * @Description       : Integration Procedure per recuperare la timeline di un incarico Unipol
 * <AUTHOR> VE
 * @Group             : 
 * @Last Modified On  : 29-09-2025
 * @Last Modified By  : VE
 * @cicd_tests urcs_IP_GetTimelineIncarico_Test
**/
global with sharing class urcs_IP_GetTimelineIncarico implements System.Callable {

    public Object call(String action, Map<String,Object> args){
        Map<String,Object> input = (Map<String,Object>) args.get('input');
        Map<String,Object> output = (Map<String,Object>) args.get('output');
        Map<String,Object> options = (Map<String,Object>) args.get('options');
        invokeMethod(input, output, options);
        return null;
    }
        
    @AuraEnabled
    global static Map<String,Object> invokeMethod(Map<String,Object> input,Map<String,Object> output, Map<String,Object> options){
        
        system.debug('input '+ JSON.serialize(input));
        system.debug('output '+ JSON.serialize(output));
        system.debug('options '+ JSON.serialize(options));
        
        Map<String,Object> setValueMap = (Map<String,Object>) input.get('Set Values');
        String method = (String) setValueMap.get('methodExecute');
        
        try{
            if(method.equalsIgnoreCase('init')){
                output = getTimelineIncarico(input, output, options);
            }
        }catch(Exception e){
            output.put('error', e.getMessage() + ' ' + e.getStackTraceString());
            System.debug('Error in urcs_IP_GetTimelineIncarico: ' + e.getMessage());
        }
        return output;
    }
    
    /**
     * Metodo principale per recuperare la timeline di un incarico
     */
    public static Map<String,Object> getTimelineIncarico(Map<String,Object> input, Map<String,Object> output, Map<String,Object> options){
        try{
            String chiavelavorazione = null != (String)input.get('chiavelavorazione') ? (String)input.get('chiavelavorazione') : null;
            String numeroIncarico = null != (String)input.get('numeroIncarico') ? (String)input.get('numeroIncarico') : null;
            String tipoIncarico = null != (String)input.get('tipoIncarico') ? (String)input.get('tipoIncarico') : null;
            
            system.debug('chiavelavorazione: ' + chiavelavorazione);
            system.debug('numeroIncarico: ' + numeroIncarico);
            system.debug('tipoIncarico: ' + tipoIncarico);
            
            // Validazione input
            if(!validateInput(input)) {
                output.put('error', 'Parametri di input mancanti. Specificare almeno chiavelavorazione o numeroIncarico.');
                output.put('success', false);
                return output;
            }
            
            // In ambiente di test, usa il mock
            if(Test.isRunningTest()) {
                String mockResponse;
                
                // Se è specificato il tipo incarico, usa milestone specifiche
                if(tipoIncarico != null && (tipoIncarico == 'CAR' || tipoIncarico == 'MEC' || tipoIncarico == 'CRI')) {
                    mockResponse = urcs_TimelineIncaricoMock.getMockMilestoneByTipo(tipoIncarico);
                } else {
                    mockResponse = urcs_TimelineIncaricoMock.getMockElencoMilestone();
                }
                
                Map<String, Object> responseData = (Map<String, Object>) JSON.deserializeUntyped(mockResponse);
                
                output.put('elencoMilestone', responseData.get('elencoMilestone'));
                output.put('success', true);
                output.put('message', 'Timeline recuperata con successo (mock)');
                output.put('chiavelavorazione', chiavelavorazione);
                
                system.debug('Mock response: ' + mockResponse);
                
            } else {
                // In ambiente reale, qui faresti la chiamata al servizio esterno
                // Per ora restituiamo il mock anche in produzione per testing
                String mockResponse;
                
                // Se è specificato il tipo incarico, usa milestone specifiche
                if(tipoIncarico != null && (tipoIncarico == 'CAR' || tipoIncarico == 'MEC' || tipoIncarico == 'CRI')) {
                    mockResponse = urcs_TimelineIncaricoMock.getMockMilestoneByTipo(tipoIncarico);
                } else {
                    mockResponse = urcs_TimelineIncaricoMock.getMockElencoMilestone();
                }
                
                Map<String, Object> responseData = (Map<String, Object>) JSON.deserializeUntyped(mockResponse);
                
                output.put('elencoMilestone', responseData.get('elencoMilestone'));
                output.put('success', true);
                output.put('message', 'Timeline recuperata con successo');
                output.put('chiavelavorazione', chiavelavorazione);
                
                // TODO: Implementare la chiamata reale al servizio Unipol
                // HttpRequest req = new HttpRequest();
                // req.setEndpoint('https://api.unipol.it/interventi/timeline');
                // req.setMethod('GET');
                // req.setHeader('Content-Type', 'application/json');
                // 
                // // Aggiungi parametri di ricerca
                // String endpoint = req.getEndpoint();
                // if(chiavelavorazione != null) {
                //     endpoint += '?chiavelavorazione=' + EncodingUtil.urlEncode(chiavelavorazione, 'UTF-8');
                // } else if(numeroIncarico != null) {
                //     endpoint += '?numeroIncarico=' + EncodingUtil.urlEncode(numeroIncarico, 'UTF-8');
                // }
                // req.setEndpoint(endpoint);
                // 
                // Http http = new Http();
                // HttpResponse res = http.send(req);
                // 
                // if(res.getStatusCode() == 200) {
                //     Map<String, Object> responseData = (Map<String, Object>) JSON.deserializeUntyped(res.getBody());
                //     output.put('elencoMilestone', responseData.get('elencoMilestone'));
                //     output.put('success', true);
                //     output.put('chiavelavorazione', chiavelavorazione);
                // } else {
                //     output.put('error', 'Errore nella chiamata al servizio: ' + res.getStatus());
                //     output.put('success', false);
                // }
            }
            
        } catch (Exception e){
            output.put('elencoMilestone', null);
            output.put('success', false);
            output.put('error', e.getMessage());
            System.debug('Error in urcs_IP_GetTimelineIncarico method getTimelineIncarico: '+ json.serializePretty(e));
        }

        return output;
    }
    
    /**
     * Metodo helper per validare i parametri di input
     */
    private static Boolean validateInput(Map<String,Object> input) {
        String chiavelavorazione = (String)input.get('chiavelavorazione');
        String numeroIncarico = (String)input.get('numeroIncarico');
        
        // Almeno uno dei parametri deve essere presente
        return (chiavelavorazione != null && chiavelavorazione != '') || 
               (numeroIncarico != null && numeroIncarico != '');
    }
    
    /**
     * Metodo per ordinare le milestone per data
     */
    private static List<Object> sortMilestonesByDate(List<Object> milestones) {
        if(milestones == null || milestones.isEmpty()) {
            return milestones;
        }
        
        // Converte in lista di mappe per facilitare l'ordinamento
        List<Map<String, Object>> milestoneList = new List<Map<String, Object>>();
        for(Object milestoneObj : milestones) {
            milestoneList.add((Map<String, Object>) milestoneObj);
        }
        
        // Ordina per data milestone (più recenti prima)
        milestoneList.sort(new MilestoneComparator());
        
        // Riconverte in lista di oggetti
        List<Object> sortedList = new List<Object>();
        for(Map<String, Object> milestone : milestoneList) {
            sortedList.add(milestone);
        }
        
        return sortedList;
    }
    
    /**
     * Classe comparator per ordinare le milestone per data
     */
    public class MilestoneComparator implements Comparator<Map<String, Object>> {
        public Integer compare(Map<String, Object> m1, Map<String, Object> m2) {
            String date1 = (String) m1.get('dataMilestone');
            String date2 = (String) m2.get('dataMilestone');

            if(date1 == null && date2 == null) return 0;
            if(date1 == null) return 1;
            if(date2 == null) return -1;

            try {
                Date d1 = Date.valueOf(date1);
                Date d2 = Date.valueOf(date2);

                // Confronto manuale delle date (ordine decrescente - più recenti prima)
                if(d1 == d2) return 0;
                if(d1 > d2) return -1; // d1 più recente, va prima
                return 1; // d2 più recente, va prima
            } catch(Exception e) {
                // Fallback su confronto stringa
                return date2.compareTo(date1);
            }
        }
    }
}
