/**
 * Test class per urcs_IP_GetElencoLavorazioni
 */
@isTest
private class urcs_IP_GetElencoLavorazioni_Test {
    
    @isTest
    static void testCallMethod() {
        // Setup test data
        Map<String, Object> input = new Map<String, Object>{
            'recordId' => 'test-record-id',
            'vehicleId' => 'test-vehicle-id',
            'Set Values' => new Map<String, Object>{
                'methodExecute' => 'init'
            }
        };
        
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();
        
        Map<String, Object> args = new Map<String, Object>{
            'input' => input,
            'output' => output,
            'options' => options
        };
        
        urcs_IP_GetElencoLavorazioni controller = new urcs_IP_GetElencoLavorazioni();
        
        Test.startTest();
        Object result = controller.call('testAction', args);
        Test.stopTest();
        
        // Verifica che il metodo sia stato eseguito senza errori
        System.assertNotEquals(null, output, 'Output should not be null');
        System.assertEquals(true, output.get('success'), 'Success should be true');
        System.assertNotEquals(null, output.get('elencoLavorazioni'), 'ElencoLavorazioni should not be null');
        
        // Verifica la struttura dei dati
        List<Object> lavorazioni = (List<Object>) output.get('elencoLavorazioni');
        System.assertEquals(5, lavorazioni.size(), 'Should have 5 lavorazioni');
        
        // Verifica il primo elemento
        Map<String, Object> primaLavorazione = (Map<String, Object>) lavorazioni[0];
        System.assertEquals('FA213BB', primaLavorazione.get('targa'), 'First lavorazione should have correct targa');
        System.assertEquals('Mario Rossi', primaLavorazione.get('danneggiato'), 'First lavorazione should have correct danneggiato');
    }
    
    @isTest
    static void testInvokeMethodDirectly() {
        // Test del metodo invokeMethod direttamente
        Map<String, Object> input = new Map<String, Object>{
            'recordId' => 'test-record-id',
            'Set Values' => new Map<String, Object>{
                'methodExecute' => 'init'
            }
        };
        
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();
        
        Test.startTest();
        Map<String, Object> result = urcs_IP_GetElencoLavorazioni.invokeMethod(input, output, options);
        Test.stopTest();
        
        // Verifica il risultato
        System.assertNotEquals(null, result, 'Result should not be null');
        System.assertEquals(true, result.get('success'), 'Success should be true');
        System.assertNotEquals(null, result.get('elencoLavorazioni'), 'ElencoLavorazioni should not be null');
    }
    
    @isTest
    static void testGetElencoLavorazioniWithDifferentParams() {
        // Test con parametri diversi
        Map<String, Object> input1 = new Map<String, Object>{
            'vehicleId' => 'VEH001',
            'Set Values' => new Map<String, Object>{
                'methodExecute' => 'init'
            }
        };
        
        Map<String, Object> input2 = new Map<String, Object>{
            'contractId' => 'CON001',
            'Set Values' => new Map<String, Object>{
                'methodExecute' => 'init'
            }
        };
        
        Map<String, Object> output1 = new Map<String, Object>();
        Map<String, Object> output2 = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();
        
        Test.startTest();
        urcs_IP_GetElencoLavorazioni.getElencoLavorazioni(input1, output1, options);
        urcs_IP_GetElencoLavorazioni.getElencoLavorazioni(input2, output2, options);
        Test.stopTest();
        
        // Verifica che entrambe le chiamate abbiano successo
        System.assertEquals(true, output1.get('success'), 'First call should succeed');
        System.assertEquals(true, output2.get('success'), 'Second call should succeed');
        
        // Verifica che entrambe restituiscano dati
        System.assertNotEquals(null, output1.get('elencoLavorazioni'), 'First call should return data');
        System.assertNotEquals(null, output2.get('elencoLavorazioni'), 'Second call should return data');
    }
    
    @isTest
    static void testErrorHandling() {
        // Test gestione errori con metodo non valido
        Map<String, Object> input = new Map<String, Object>{
            'recordId' => 'test-record-id',
            'Set Values' => new Map<String, Object>{
                'methodExecute' => 'invalidMethod'
            }
        };
        
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();
        
        Test.startTest();
        Map<String, Object> result = urcs_IP_GetElencoLavorazioni.invokeMethod(input, output, options);
        Test.stopTest();
        
        // Il metodo dovrebbe completare senza errori anche con metodo non valido
        System.assertNotEquals(null, result, 'Result should not be null');
    }
    
    @isTest
    static void testMockDataStructure() {
        // Test per verificare la struttura dei dati mock
        String mockResponse = urcs_InterventiUnipolServiceMock.getMockElencoLavorazioni();
        Map<String, Object> responseData = (Map<String, Object>) JSON.deserializeUntyped(mockResponse);
        
        System.assert(responseData.containsKey('elencoLavorazioni'), 'Response should contain elencoLavorazioni');
        
        List<Object> lavorazioni = (List<Object>) responseData.get('elencoLavorazioni');
        System.assertEquals(5, lavorazioni.size(), 'Should have 5 lavorazioni');
        
        // Verifica che ogni lavorazione abbia i campi richiesti
        for(Object lavorazioneObj : lavorazioni) {
            Map<String, Object> lavorazione = (Map<String, Object>) lavorazioneObj;
            
            System.assert(lavorazione.containsKey('numeroIncarico'), 'Should have numeroIncarico');
            System.assert(lavorazione.containsKey('targa'), 'Should have targa');
            System.assert(lavorazione.containsKey('danneggiato'), 'Should have danneggiato');
            System.assert(lavorazione.containsKey('tipoIncarico'), 'Should have tipoIncarico');
            System.assert(lavorazione.containsKey('codStatoLavorazione'), 'Should have codStatoLavorazione');
            System.assert(lavorazione.containsKey('chiavelavorazione'), 'Should have chiavelavorazione');
        }
    }
    
    @isTest
    static void testWithNullInput() {
        // Test con input nullo o vuoto
        Map<String, Object> input = new Map<String, Object>{
            'Set Values' => new Map<String, Object>{
                'methodExecute' => 'init'
            }
        };
        
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();
        
        Test.startTest();
        Map<String, Object> result = urcs_IP_GetElencoLavorazioni.getElencoLavorazioni(input, output, options);
        Test.stopTest();
        
        // Anche senza parametri specifici, dovrebbe restituire i dati mock
        System.assertEquals(true, result.get('success'), 'Should succeed even with null input');
        System.assertNotEquals(null, result.get('elencoLavorazioni'), 'Should return mock data');
    }
}
