/**
 * Test class per urcs_IP_GetTimelineIncarico
 */
@isTest
private class urcs_IP_GetTimelineIncarico_Test {
    
    @isTest
    static void testCallMethod() {
        // Setup test data
        Map<String, Object> input = new Map<String, Object>{
            'chiavelavorazione' => 'MEC_K-0001-2025-0000001',
            'tipoIncarico' => 'CAR',
            'Set Values' => new Map<String, Object>{
                'methodExecute' => 'init'
            }
        };
        
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();
        
        Map<String, Object> args = new Map<String, Object>{
            'input' => input,
            'output' => output,
            'options' => options
        };
        
        urcs_IP_GetTimelineIncarico controller = new urcs_IP_GetTimelineIncarico();
        
        Test.startTest();
        Object result = controller.call('testAction', args);
        Test.stopTest();
        
        // Verifica che il metodo sia stato eseguito senza errori
        System.assertNotEquals(null, output, 'Output should not be null');
        System.assertEquals(true, output.get('success'), 'Success should be true');
        System.assertNotEquals(null, output.get('elencoMilestone'), 'ElencoMilestone should not be null');
        System.assertEquals('MEC_K-0001-2025-0000001', output.get('chiavelavorazione'), 'Should return correct chiavelavorazione');
        
        // Verifica la struttura dei dati
        List<Object> milestones = (List<Object>) output.get('elencoMilestone');
        System.assertEquals(7, milestones.size(), 'Should have 7 milestones for CAR type');
        
        // Verifica la prima milestone
        Map<String, Object> primaMilestone = (Map<String, Object>) milestones[0];
        System.assertEquals('Creazione Incarico da Officina', primaMilestone.get('descrizione'), 'First milestone should have correct description');
    }
    
    @isTest
    static void testInvokeMethodDirectly() {
        // Test del metodo invokeMethod direttamente
        Map<String, Object> input = new Map<String, Object>{
            'numeroIncarico' => '1-8001-2025-0000001',
            'Set Values' => new Map<String, Object>{
                'methodExecute' => 'init'
            }
        };
        
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();
        
        Test.startTest();
        Map<String, Object> result = urcs_IP_GetTimelineIncarico.invokeMethod(input, output, options);
        Test.stopTest();
        
        // Verifica il risultato
        System.assertNotEquals(null, result, 'Result should not be null');
        System.assertEquals(true, result.get('success'), 'Success should be true');
        System.assertNotEquals(null, result.get('elencoMilestone'), 'ElencoMilestone should not be null');
    }
    
    @isTest
    static void testGetTimelineWithDifferentTipoIncarico() {
        // Test con diversi tipi di incarico
        Map<String, Object> inputCAR = new Map<String, Object>{
            'chiavelavorazione' => 'CAR_K-0001-2025-0000001',
            'tipoIncarico' => 'CAR',
            'Set Values' => new Map<String, Object>{
                'methodExecute' => 'init'
            }
        };
        
        Map<String, Object> inputMEC = new Map<String, Object>{
            'chiavelavorazione' => 'MEC_K-0002-2025-0000002',
            'tipoIncarico' => 'MEC',
            'Set Values' => new Map<String, Object>{
                'methodExecute' => 'init'
            }
        };
        
        Map<String, Object> inputCRI = new Map<String, Object>{
            'chiavelavorazione' => 'CRI_K-0003-2025-0000003',
            'tipoIncarico' => 'CRI',
            'Set Values' => new Map<String, Object>{
                'methodExecute' => 'init'
            }
        };
        
        Map<String, Object> outputCAR = new Map<String, Object>();
        Map<String, Object> outputMEC = new Map<String, Object>();
        Map<String, Object> outputCRI = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();
        
        Test.startTest();
        urcs_IP_GetTimelineIncarico.getTimelineIncarico(inputCAR, outputCAR, options);
        urcs_IP_GetTimelineIncarico.getTimelineIncarico(inputMEC, outputMEC, options);
        urcs_IP_GetTimelineIncarico.getTimelineIncarico(inputCRI, outputCRI, options);
        Test.stopTest();
        
        // Verifica che tutte le chiamate abbiano successo
        System.assertEquals(true, outputCAR.get('success'), 'CAR call should succeed');
        System.assertEquals(true, outputMEC.get('success'), 'MEC call should succeed');
        System.assertEquals(true, outputCRI.get('success'), 'CRI call should succeed');
        
        // Verifica che abbiano numeri diversi di milestone
        List<Object> milestonesCAR = (List<Object>) outputCAR.get('elencoMilestone');
        List<Object> milestonesMEC = (List<Object>) outputMEC.get('elencoMilestone');
        List<Object> milestonesCRI = (List<Object>) outputCRI.get('elencoMilestone');
        
        System.assertEquals(7, milestonesCAR.size(), 'CAR should have 7 milestones');
        System.assertEquals(6, milestonesMEC.size(), 'MEC should have 6 milestones');
        System.assertEquals(5, milestonesCRI.size(), 'CRI should have 5 milestones');
    }
    
    @isTest
    static void testValidationError() {
        // Test gestione errori con input non valido
        Map<String, Object> input = new Map<String, Object>{
            'Set Values' => new Map<String, Object>{
                'methodExecute' => 'init'
            }
            // Nessun chiavelavorazione o numeroIncarico
        };
        
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();
        
        Test.startTest();
        Map<String, Object> result = urcs_IP_GetTimelineIncarico.getTimelineIncarico(input, output, options);
        Test.stopTest();
        
        // Dovrebbe restituire un errore di validazione
        System.assertEquals(false, result.get('success'), 'Should fail validation');
        System.assert(((String)result.get('error')).contains('Parametri di input mancanti'), 'Should have validation error message');
    }
    
    @isTest
    static void testErrorHandling() {
        // Test gestione errori con metodo non valido
        Map<String, Object> input = new Map<String, Object>{
            'chiavelavorazione' => 'test-key',
            'Set Values' => new Map<String, Object>{
                'methodExecute' => 'invalidMethod'
            }
        };
        
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();
        
        Test.startTest();
        Map<String, Object> result = urcs_IP_GetTimelineIncarico.invokeMethod(input, output, options);
        Test.stopTest();
        
        // Il metodo dovrebbe completare senza errori anche con metodo non valido
        System.assertNotEquals(null, result, 'Result should not be null');
    }
    
    @isTest
    static void testMockDataStructure() {
        // Test per verificare la struttura dei dati mock
        String mockResponse = urcs_TimelineIncaricoMock.getMockElencoMilestone();
        Map<String, Object> responseData = (Map<String, Object>) JSON.deserializeUntyped(mockResponse);
        
        System.assert(responseData.containsKey('elencoMilestone'), 'Response should contain elencoMilestone');
        
        List<Object> milestones = (List<Object>) responseData.get('elencoMilestone');
        System.assertEquals(7, milestones.size(), 'Should have 7 milestones');
        
        // Verifica che ogni milestone abbia i campi richiesti
        for(Object milestoneObj : milestones) {
            Map<String, Object> milestone = (Map<String, Object>) milestoneObj;
            
            System.assert(milestone.containsKey('descrizione'), 'Should have descrizione');
            System.assert(milestone.containsKey('dataMilestone'), 'Should have dataMilestone');
            System.assert(milestone.containsKey('codStato'), 'Should have codStato');
            System.assert(milestone.containsKey('descrizioneStato'), 'Should have descrizioneStato');
            System.assert(milestone.containsKey('descrizioneAggiuntiva'), 'Should have descrizioneAggiuntiva');
        }
    }
    
    @isTest
    static void testMockDataByTipoIncarico() {
        // Test dei dati mock specifici per tipo incarico
        String mockCAR = urcs_TimelineIncaricoMock.getMockMilestoneByTipo('CAR');
        String mockMEC = urcs_TimelineIncaricoMock.getMockMilestoneByTipo('MEC');
        String mockCRI = urcs_TimelineIncaricoMock.getMockMilestoneByTipo('CRI');
        
        Map<String, Object> dataCAR = (Map<String, Object>) JSON.deserializeUntyped(mockCAR);
        Map<String, Object> dataMEC = (Map<String, Object>) JSON.deserializeUntyped(mockMEC);
        Map<String, Object> dataCRI = (Map<String, Object>) JSON.deserializeUntyped(mockCRI);
        
        List<Object> milestonesCAR = (List<Object>) dataCAR.get('elencoMilestone');
        List<Object> milestonesMEC = (List<Object>) dataMEC.get('elencoMilestone');
        List<Object> milestonesCRI = (List<Object>) dataCRI.get('elencoMilestone');
        
        System.assertEquals(7, milestonesCAR.size(), 'CAR should have 7 milestones');
        System.assertEquals(6, milestonesMEC.size(), 'MEC should have 6 milestones');
        System.assertEquals(5, milestonesCRI.size(), 'CRI should have 5 milestones');
        
        // Verifica milestone specifiche per carrozzeria
        Map<String, Object> carFirstMilestone = (Map<String, Object>) milestonesCAR[0];
        System.assert(((String) carFirstMilestone.get('descrizioneAggiuntiva')).contains('carrozzeria'), 
                     'CAR milestone should contain carrozzeria reference');
    }
}
