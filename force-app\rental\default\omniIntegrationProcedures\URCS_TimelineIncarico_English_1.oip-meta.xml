<?xml version="1.0" encoding="UTF-8"?>
<OmniIntegrationProcedure xmlns="http://soap.sforce.com/2006/04/metadata">
    <elementTypeComponentMapping>{"ElementTypeToHTMLTemplateList":[]}</elementTypeComponentMapping>
    <isActive>true</isActive>
    <isIntegProcdSignatureAvl>false</isIntegProcdSignatureAvl>
    <isIntegrationProcedure>true</isIntegrationProcedure>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <isMetadataCacheDisabled>false</isMetadataCacheDisabled>
    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
    <isTestProcedure>false</isTestProcedure>
    <isWebCompEnabled>false</isWebCompEnabled>
    <language>English</language>
    <name>urcs_IP_GetTimelineIncarico</name>
    <omniProcessElements>
        <description>Set Values</description>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetValues</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  "executionConditionalFormula" : "",
  "failureConditionalFormula" : "",
  "failOnStepError" : false,
  "id" : "",
  "internalNotes" : "",
  "isActive" : true,
  "restOptions" : { },
  "chainOnStep" : false,
  "setValueActions" : [ {
    "isActive" : true,
    "setValueActionType" : "Set",
    "targetKey" : "methodExecute",
    "targetValue" : "init"
  } ]
}</propertySetConfig>
        <sequenceNumber>1.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <description>Remote Action</description>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>Remote</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  "sendOnlyAdditionalInput" : false,
  "remoteMethod" : "invokeMethod",
  "returnOnlyAdditionalOutput" : false,
  "failOnStepError" : false,
  "internalNotes" : "",
  "isActive" : true,
  "chainOnStep" : false,
  "returnOnlyFailureResponse" : false,
  "responseJSONPath" : "",
  "responseJSONNode" : "",
  "executionConditionalFormula" : "",
  "remoteClass" : "urcs_IP_GetTimelineIncarico",
  "actionMessage" : "",
  "sendJSONPath" : "",
  "failureConditionalFormula" : "",
  "id" : "",
  "restOptions" : { },
  "sendJSONNode" : ""
}</propertySetConfig>
        <sequenceNumber>2.0</sequenceNumber>
        <type>Remote Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <description>Response Action</description>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>Response</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  "responseJSONPath" : "",
  "responseJSONNode" : "",
  "executionConditionalFormula" : "",
  "failureConditionalFormula" : "",
  "failOnStepError" : false,
  "id" : "",
  "internalNotes" : "",
  "isActive" : true,
  "restOptions" : { },
  "chainOnStep" : false
}</propertySetConfig>
        <sequenceNumber>3.0</sequenceNumber>
        <type>Response Action</type>
    </omniProcessElements>
    <omniProcessKey>URCS_TimelineIncarico</omniProcessKey>
    <omniProcessType>Integration Procedure</omniProcessType>
    <propertySetConfig>{
  "transientValues" : {
    "deactivateConsent" : false
  }
}</propertySetConfig>
    <subType>TimelineIncarico</subType>
    <type>URCS</type>
    <uniqueName>URCS_TimelineIncarico_English_1</uniqueName>
    <versionNumber>1.0</versionNumber>
</OmniIntegrationProcedure>
