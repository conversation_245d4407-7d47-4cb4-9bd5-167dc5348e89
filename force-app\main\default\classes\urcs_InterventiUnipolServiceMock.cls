/**
 * Mock class per simulare le risposte del servizio Unipol per gli interventi
 * Utilizzata per testare le flexcard urcs_InterventiUnipolServiceParent e urcs_InterventiUnipolServiceChild
 */
@isTest
public class urcs_InterventiUnipolServiceMock implements HttpCalloutMock {
    
    public HTTPResponse respond(HTTPRequest req) {
        HttpResponse res = new HttpResponse();
        res.setHeader('Content-Type', 'application/json');
        res.setStatusCode(200);
        
        // Simula la risposta per l'elenco lavorazioni (header per parent/child)
        String mockResponse = generateElencoLavorazioniResponse();
        res.setBody(mockResponse);
        
        return res;
    }
    
    /**
     * Genera la risposta mock per l'elenco delle lavorazioni
     */
    private String generateElencoLavorazioniResponse() {
        Map<String, Object> response = new Map<String, Object>();
        List<Map<String, Object>> elencoLavorazioni = new List<Map<String, Object>>();
        
        // Elemento 1 - Carrozzeria
        Map<String, Object> lavorazione1 = new Map<String, Object>();
        lavorazione1.put('numeroIncarico', '1-8001-2025-0000001 (Incarico CAR), K-0001-2025-0000001 (Incarico MEC)');
        lavorazione1.put('targa', 'FA213BB');
        lavorazione1.put('danneggiato', 'Mario Rossi');
        lavorazione1.put('marcaModelloAllestimento', 'Alfa Romeo Giulia Quadrifoglio Verde');
        lavorazione1.put('codiceCentroFiduciario', '99727');
        lavorazione1.put('denominazioneCentroFiduciario', 'Carrozzeria Fratelli Rossi');
        lavorazione1.put('dataAssegnazione', '2024-11-29');
        lavorazione1.put('codStatoLavorazione', 'RIPRE');
        lavorazione1.put('descrizioneStatoLavorazione', 'Nuovo');
        lavorazione1.put('tipoIncarico', 'CAR');
        lavorazione1.put('descrizioneTipoIncarico', 'Riparazione di Carrozzeria');
        lavorazione1.put('chiavelavorazione', 'MEC_K-0001-2025-0000001');
        lavorazione1.put('condizioneVeicolo', 'Non Marciante');
        
        // Elemento 2 - Meccanica
        Map<String, Object> lavorazione2 = new Map<String, Object>();
        lavorazione2.put('numeroIncarico', '1-8002-2025-0000002 (Incarico MEC)');
        lavorazione2.put('targa', 'GH456CD');
        lavorazione2.put('danneggiato', 'Anna Verdi');
        lavorazione2.put('marcaModelloAllestimento', 'BMW X3 xDrive20d');
        lavorazione2.put('codiceCentroFiduciario', '99728');
        lavorazione2.put('denominazioneCentroFiduciario', 'Officina Meccanica Bianchi');
        lavorazione2.put('dataAssegnazione', '2024-12-01');
        lavorazione2.put('codStatoLavorazione', 'INLAV');
        lavorazione2.put('descrizioneStatoLavorazione', 'In Lavorazione');
        lavorazione2.put('tipoIncarico', 'MEC');
        lavorazione2.put('descrizioneTipoIncarico', 'Riparazione Meccanica');
        lavorazione2.put('chiavelavorazione', 'MEC_K-0002-2025-0000002');
        lavorazione2.put('condizioneVeicolo', 'Marciante');
        
        // Elemento 3 - Cristalli
        Map<String, Object> lavorazione3 = new Map<String, Object>();
        lavorazione3.put('numeroIncarico', '1-8003-2025-0000003 (Incarico CRI)');
        lavorazione3.put('targa', 'LM789EF');
        lavorazione3.put('danneggiato', 'Giuseppe Neri');
        lavorazione3.put('marcaModelloAllestimento', 'Mercedes-Benz Classe A 180d');
        lavorazione3.put('codiceCentroFiduciario', '99729');
        lavorazione3.put('denominazioneCentroFiduciario', 'Cristalleria Moderna');
        lavorazione3.put('dataAssegnazione', '2024-12-03');
        lavorazione3.put('codStatoLavorazione', 'COMPL');
        lavorazione3.put('descrizioneStatoLavorazione', 'Completato');
        lavorazione3.put('tipoIncarico', 'CRI');
        lavorazione3.put('descrizioneTipoIncarico', 'Sostituzione Cristalli');
        lavorazione3.put('chiavelavorazione', 'CRI_K-0003-2025-0000003');
        lavorazione3.put('condizioneVeicolo', 'Marciante');
        
        // Elemento 4 - Elettrauto
        Map<String, Object> lavorazione4 = new Map<String, Object>();
        lavorazione4.put('numeroIncarico', '1-8004-2025-0000004 (Incarico ELE)');
        lavorazione4.put('targa', 'PQ012GH');
        lavorazione4.put('danneggiato', 'Laura Blu');
        lavorazione4.put('marcaModelloAllestimento', 'Audi A4 Avant 2.0 TDI');
        lavorazione4.put('codiceCentroFiduciario', '99730');
        lavorazione4.put('denominazioneCentroFiduciario', 'Elettrauto Specializzato');
        lavorazione4.put('dataAssegnazione', '2024-12-05');
        lavorazione4.put('codStatoLavorazione', 'SOSPE');
        lavorazione4.put('descrizioneStatoLavorazione', 'Sospeso');
        lavorazione4.put('tipoIncarico', 'ELE');
        lavorazione4.put('descrizioneTipoIncarico', 'Riparazione Elettrica');
        lavorazione4.put('chiavelavorazione', 'ELE_K-0004-2025-0000004');
        lavorazione4.put('condizioneVeicolo', 'Non Marciante');
        
        // Elemento 5 - Gommista
        Map<String, Object> lavorazione5 = new Map<String, Object>();
        lavorazione5.put('numeroIncarico', '1-8005-2025-0000005 (Incarico GOM)');
        lavorazione5.put('targa', 'ST345IJ');
        lavorazione5.put('danneggiato', 'Roberto Gialli');
        lavorazione5.put('marcaModelloAllestimento', 'Volkswagen Golf 1.6 TDI');
        lavorazione5.put('codiceCentroFiduciario', '99731');
        lavorazione5.put('denominazioneCentroFiduciario', 'Pneumatici & Servizi');
        lavorazione5.put('dataAssegnazione', '2024-12-07');
        lavorazione5.put('codStatoLavorazione', 'RIPRE');
        lavorazione5.put('descrizioneStatoLavorazione', 'Nuovo');
        lavorazione5.put('tipoIncarico', 'GOM');
        lavorazione5.put('descrizioneTipoIncarico', 'Sostituzione Pneumatici');
        lavorazione5.put('chiavelavorazione', 'GOM_K-0005-2025-0000005');
        lavorazione5.put('condizioneVeicolo', 'Marciante');
        
        elencoLavorazioni.add(lavorazione1);
        elencoLavorazioni.add(lavorazione2);
        elencoLavorazioni.add(lavorazione3);
        elencoLavorazioni.add(lavorazione4);
        elencoLavorazioni.add(lavorazione5);
        
        response.put('elencoLavorazioni', elencoLavorazioni);
        
        return JSON.serialize(response);
    }
    
    /**
     * Metodo statico per ottenere una risposta mock specifica per i test
     */
    public static String getMockElencoLavorazioni() {
        urcs_InterventiUnipolServiceMock mock = new urcs_InterventiUnipolServiceMock();
        return mock.generateElencoLavorazioniResponse();
    }
}
