<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Developer</authorName>
    <clonedFromOmniUiCardKey>urcs_TabellaParentNoTable/Developer/1.0</clonedFromOmniUiCardKey>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;resultVar&quot;:&quot;[\&quot;Remote\&quot;][\&quot;data\&quot;]&quot;,&quot;ipMethod&quot;:&quot;URCS_Sinistri&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;recordId&quot;:&quot;{recordId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;02i9V000006yF8nQAE&quot;,&quot;id&quot;:2}]},&quot;event-0_1&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;resultVar&quot;:&quot;[\&quot;Remote\&quot;][\&quot;recordDetails\&quot;]&quot;,&quot;ipMethod&quot;:&quot;URCS_SinistriDetails&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;selectedSinistriId&quot;:&quot;{selectedSinistriId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;selectedSinistriId\&quot;:\&quot;qDS000004066584GAA\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;selectedSinistriId&quot;,&quot;val&quot;:&quot;qDS000004066584GAA&quot;,&quot;id&quot;:2}]}}</dataSourceConfig>
    <isActive>false</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>urcs_InterventiUnipolServiceParent</name>
    <omniUiCardType>Parent</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;theme&quot;:&quot;&quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;padding&quot;:{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;},&quot;margin&quot;:{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;x-small&quot;},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;height&quot;:&quot;&quot;,&quot;minHeight&quot;:&quot;&quot;,&quot;maxHeight&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-p-around_x-small slds-card slds-m-bottom_x-small slds-col &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;selectedStyles&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;&quot;,&quot;elementStyleProperties&quot;:{},&quot;inlineStyle&quot;:&quot;&quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Data Table&quot;,&quot;element&quot;:&quot;flexDatatable&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;issearchavailable&quot;:&quot;false&quot;,&quot;issortavailable&quot;:&quot;true&quot;,&quot;records&quot;:&quot;{records}&quot;,&quot;columns&quot;:[]},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Data Table-0&quot;,&quot;uKey&quot;:&quot;1750235645597-656&quot;,&quot;datasourceKey&quot;:&quot;state0element0&quot;},{&quot;name&quot;:&quot;Data Table&quot;,&quot;element&quot;:&quot;flexDatatable&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;issearchavailable&quot;:false,&quot;issortavailable&quot;:true,&quot;cellLevelEdit&quot;:true,&quot;pagelimit&quot;:3,&quot;groupOrder&quot;:&quot;asc&quot;,&quot;searchDatatable&quot;:&quot;&quot;,&quot;columns&quot;:[],&quot;records&quot;:&quot;{records}&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Data Table-1&quot;,&quot;uKey&quot;:&quot;1750235645597-294&quot;,&quot;datasourceKey&quot;:&quot;state0element1&quot;},{&quot;name&quot;:&quot;Data Table&quot;,&quot;element&quot;:&quot;flexDatatable&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;issearchavailable&quot;:&quot;false&quot;,&quot;issortavailable&quot;:&quot;true&quot;,&quot;records&quot;:&quot;{records}&quot;,&quot;columns&quot;:[]},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Data Table-2&quot;,&quot;uKey&quot;:&quot;1750235645597-350&quot;,&quot;datasourceKey&quot;:&quot;state0element2&quot;},{&quot;name&quot;:&quot;Flexcard&quot;,&quot;element&quot;:&quot;childCardPreview&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;cardName&quot;:&quot;urcs_InterventiUnipolServiceChild&quot;,&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;cardNode&quot;:&quot;&quot;,&quot;selectedState&quot;:&quot;Active&quot;,&quot;isChildCardTrackingEnabled&quot;:false},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;datasourceKey&quot;:&quot;state0element3&quot;,&quot;uKey&quot;:&quot;1759139973729-399&quot;,&quot;elementLabel&quot;:&quot;Flexcard-3&quot;}]}},&quot;childCards&quot;:[&quot;urcs_InterventiUnipolServiceChild&quot;],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;resultVar&quot;:&quot;[\&quot;Remote\&quot;][\&quot;data\&quot;]&quot;,&quot;ipMethod&quot;:&quot;URCS_Sinistri&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;recordId&quot;:&quot;{recordId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;02i9V000006yF8nQAE&quot;,&quot;id&quot;:2}]},&quot;title&quot;:&quot;urcs_InterventiUnipolServiceParent&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Single&quot;,&quot;xmlObject&quot;:{&quot;masterLabel&quot;:&quot;urcs_TabellaSinistri&quot;,&quot;apiVersion&quot;:61,&quot;targetConfigs&quot;:&quot;PHRhcmdldENvbmZpZyAgeG1sbnM9Imh0dHA6Ly9zb2FwLnNmb3JjZS5jb20vMjAwNi8wNC9tZXRhZGF0YSIgdGFyZ2V0cz0ibGlnaHRuaW5nX19BcHBQYWdlIj48cHJvcGVydHkgIG5hbWU9ImRlYnVnIiB0eXBlPSJCb29sZWFuIj48L3Byb3BlcnR5Pjxwcm9wZXJ0eSAgbmFtZT0icmVjb3JkSWQiIHR5cGU9IlN0cmluZyI+PC9wcm9wZXJ0eT48L3RhcmdldENvbmZpZz48dGFyZ2V0Q29uZmlnICB4bWxucz0iaHR0cDovL3NvYXAuc2ZvcmNlLmNvbS8yMDA2LzA0L21ldGFkYXRhIiB0YXJnZXRzPSJsaWdodG5pbmdfX1JlY29yZFBhZ2UiPjxwcm9wZXJ0eSAgbmFtZT0iZGVidWciIHR5cGU9IkJvb2xlYW4iPjwvcHJvcGVydHk+PG9iamVjdHMgPjxvYmplY3Q+QXNzZXQ8L29iamVjdD48L29iamVjdHM+PC90YXJnZXRDb25maWc+&quot;,&quot;targets&quot;:{&quot;target&quot;:[&quot;lightning__RecordPage&quot;,&quot;lightning__AppPage&quot;,&quot;lightning__HomePage&quot;]},&quot;isExplicitImport&quot;:false,&quot;description&quot;:&quot;&quot;},&quot;xmlJson&quot;:[{&quot;@attributes&quot;:{&quot;xmlns&quot;:&quot;http://soap.sforce.com/2006/04/metadata&quot;,&quot;targets&quot;:&quot;lightning__AppPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}}]},{&quot;@attributes&quot;:{&quot;xmlns&quot;:&quot;http://soap.sforce.com/2006/04/metadata&quot;,&quot;targets&quot;:&quot;lightning__RecordPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}}],&quot;objects&quot;:{&quot;@attributes&quot;:{&quot;xmlns&quot;:&quot;&quot;},&quot;object&quot;:&quot;Asset&quot;}}],&quot;selectedCardsLabel&quot;:&quot;{records}&quot;,&quot;events&quot;:[],&quot;isRepeatable&quot;:true,&quot;selectableField&quot;:&quot;&quot;,&quot;sessionVars&quot;:[]}</propertySetConfig>
    <sampleDataSourceResponse>{&quot;RemoteStatus&quot;:true,&quot;Set ValuesStatus&quot;:true,&quot;Remote&quot;:{&quot;error&quot;:&quot;OK&quot;,&quot;errorCode&quot;:&quot;INVOKE-200&quot;,&quot;data&quot;:[{&quot;DS_CANALE_APERTURA__c&quot;:&quot;Leonardo&quot;,&quot;TS_CHIUSURA__c&quot;:&quot;2016-03-09T15:29:00.000+0000&quot;,&quot;TS_CREAZIONE__c&quot;:&quot;2016-03-09T15:22:00.000+0000&quot;,&quot;TS_DENUNCIA__c&quot;:&quot;2016-03-09T00:00:00.000+0000&quot;,&quot;TS_INVIO_WB__c&quot;:null,&quot;DS_ULTIMA_SORGENTE__c&quot;:&quot;LEO&quot;,&quot;INTERVENTO_DATA_INVIO__c&quot;:&quot;2436939 | Non inviato&quot;,&quot;DS_DRIVER_DENOMINAZIONECOMPUTED__c&quot;:&quot;Renosto Stefano&quot;,&quot;ID_AUTO__c&quot;:434076,&quot;ID_CONTRATTO__c&quot;:1073479,&quot;DS_STATO_SINISTRO__c&quot;:&quot;Certificato&quot;,&quot;ID_SINISTRO_WB__c&quot;:null,&quot;ID_SINISTRO__c&quot;:192488,&quot;attributes&quot;:{&quot;url&quot;:&quot;/services/data/v65.0/sobjects/UR_SINISTRI__dlm/qDS000007666988GAA&quot;,&quot;type&quot;:&quot;UR_SINISTRI__dlm&quot;}}]},&quot;Set Values&quot;:{&quot;methodExecute&quot;:&quot;init&quot;,&quot;recordId&quot;:&quot;02i9V000006yF8nQAE&quot;},&quot;options&quot;:{&quot;forceQueueable&quot;:false,&quot;mockHttpResponse&quot;:null,&quot;vlcApexResponse&quot;:true,&quot;useFuture&quot;:false,&quot;isTestProcedure&quot;:false,&quot;resetCache&quot;:false,&quot;integrationProcedureKey&quot;:null,&quot;vlcIPData&quot;:null,&quot;OmniAnalyticsTrackingDebug&quot;:false,&quot;ignoreCache&quot;:false,&quot;shouldCommit&quot;:false,&quot;vlcTestSuiteUniqueKey&quot;:null,&quot;vlcTestUniqueKey&quot;:null,&quot;vlcCacheKey&quot;:null,&quot;continuationStepResult&quot;:null,&quot;vlcFilesMap&quot;:null,&quot;ParentInteractionToken&quot;:null,&quot;useQueueable&quot;:false,&quot;disableMetadataCache&quot;:false,&quot;isDebug&quot;:false,&quot;queueableChainable&quot;:false,&quot;useContinuation&quot;:false,&quot;chainable&quot;:false,&quot;ignoreMetadataPermissions&quot;:false,&quot;useHttpCalloutMock&quot;:false,&quot;useQueueableApexRemoting&quot;:false},&quot;response&quot;:{},&quot;ResponseStatus&quot;:true,&quot;recordId&quot;:&quot;02i9V000006yF8nQAE&quot;}</sampleDataSourceResponse>
    <stylingConfiguration>{&quot;styleObjects&quot;:{&quot;urcs_textstyle&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;bottom:x-small&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small&quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;selectedStyles&quot;:&quot;urcs_textstyle&quot;,&quot;element&quot;:&quot;state&quot;}}}</stylingConfiguration>
    <versionNumber>1</versionNumber>
</OmniUiCard>
