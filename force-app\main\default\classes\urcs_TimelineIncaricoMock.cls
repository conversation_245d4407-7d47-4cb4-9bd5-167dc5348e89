/**
 * Mock class per simulare le risposte del servizio timeline incarichi
 * Utilizzata per testare la flexcard urcs_TimelineIncarico
 */
@isTest
public class urcs_TimelineIncaricoMock implements HttpCalloutMock {
    
    private String chiavelavorazione;
    
    public urcs_TimelineIncaricoMock() {
        this.chiavelavorazione = 'MEC_K-0001-2025-0000001'; // Default
    }
    
    public urcs_TimelineIncaricoMock(String chiavelavorazione) {
        this.chiavelavorazione = chiavelavorazione;
    }
    
    public HTTPResponse respond(HTTPRequest req) {
        HttpResponse res = new HttpResponse();
        res.setHeader('Content-Type', 'application/json');
        res.setStatusCode(200);
        
        // Simula la risposta per l'elenco milestone (timeline dettagli)
        String mockResponse = generateElencoMilestoneResponse();
        res.setBody(mockResponse);
        
        return res;
    }
    
    /**
     * Genera la risposta mock per l'elenco delle milestone
     */
    private String generateElencoMilestoneResponse() {
        Map<String, Object> response = new Map<String, Object>();
        List<Map<String, Object>> elencoMilestone = new List<Map<String, Object>>();
        
        // Milestone 1 - Creazione Incarico da Officina
        Map<String, Object> milestone1 = new Map<String, Object>();
        milestone1.put('descrizione', 'Creazione Incarico da Officina');
        milestone1.put('dataMilestone', '2024-12-05');
        milestone1.put('codStato', 'C');
        milestone1.put('descrizioneStato', 'completed');
        milestone1.put('descrizioneAggiuntiva', 'Incarico creato e assegnato all\'officina autorizzata');
        
        // Milestone 2 - Check-in
        Map<String, Object> milestone2 = new Map<String, Object>();
        milestone2.put('descrizione', 'Check-in');
        milestone2.put('dataMilestone', '2024-12-03');
        milestone2.put('codStato', 'C');
        milestone2.put('descrizioneStato', 'completed');
        milestone2.put('descrizioneAggiuntiva', 'Veicolo preso in carico dall\'officina');
        
        // Milestone 3 - Preventivo Autorizzato
        Map<String, Object> milestone3 = new Map<String, Object>();
        milestone3.put('descrizione', 'Preventivo Autorizzato');
        milestone3.put('dataMilestone', '2024-12-10');
        milestone3.put('codStato', 'P');
        milestone3.put('descrizioneStato', 'pending');
        milestone3.put('descrizioneAggiuntiva', 'Preventivo in lavorazione per integrazione danno');
        
        // Milestone 4 - Consegna Ricambi
        Map<String, Object> milestone4 = new Map<String, Object>();
        milestone4.put('descrizione', 'Consegna Ricambi');
        milestone4.put('dataMilestone', '2024-12-11');
        milestone4.put('codStato', 'P');
        milestone4.put('descrizioneStato', 'pending');
        milestone4.put('descrizioneAggiuntiva', 'In attesa della consegna dei ricambi ordinati');
        
        // Milestone 5 - Avvio Lavori
        Map<String, Object> milestone5 = new Map<String, Object>();
        milestone5.put('descrizione', 'Avvio Lavori');
        milestone5.put('dataMilestone', '2024-12-03');
        milestone5.put('codStato', 'C');
        milestone5.put('descrizioneStato', 'completed');
        milestone5.put('descrizioneAggiuntiva', 'Lavori di riparazione avviati');
        
        // Milestone 6 - Auto Pronta
        Map<String, Object> milestone6 = new Map<String, Object>();
        milestone6.put('descrizione', 'Auto Pronta');
        milestone6.put('dataMilestone', '2025-01-11');
        milestone6.put('codStato', 'S');
        milestone6.put('descrizioneStato', 'scheduled');
        milestone6.put('descrizioneAggiuntiva', 'Data prevista per il completamento dei lavori');
        
        // Milestone 7 - Auto Restituita
        Map<String, Object> milestone7 = new Map<String, Object>();
        milestone7.put('descrizione', 'Auto Restituita');
        milestone7.put('dataMilestone', '2025-01-02');
        milestone7.put('codStato', 'S');
        milestone7.put('descrizioneStato', 'scheduled');
        milestone7.put('descrizioneAggiuntiva', 'Data prevista per la restituzione del veicolo');
        
        elencoMilestone.add(milestone1);
        elencoMilestone.add(milestone2);
        elencoMilestone.add(milestone3);
        elencoMilestone.add(milestone4);
        elencoMilestone.add(milestone5);
        elencoMilestone.add(milestone6);
        elencoMilestone.add(milestone7);
        
        response.put('elencoMilestone', elencoMilestone);
        
        return JSON.serialize(response);
    }
    
    /**
     * Genera milestone specifiche per diversi tipi di incarico
     */
    public String generateMilestoneByTipoIncarico(String tipoIncarico) {
        Map<String, Object> response = new Map<String, Object>();
        List<Map<String, Object>> elencoMilestone = new List<Map<String, Object>>();
        
        if (tipoIncarico == 'CAR') {
            // Milestone specifiche per carrozzeria
            elencoMilestone.add(createMilestone('Creazione Incarico da Officina', '2024-11-29', 'C', 'completed', 'Incarico carrozzeria creato'));
            elencoMilestone.add(createMilestone('Check-in', '2024-12-01', 'C', 'completed', 'Veicolo preso in carico'));
            elencoMilestone.add(createMilestone('Preventivo Autorizzato', '2024-12-05', 'C', 'completed', 'Preventivo approvato per riparazione carrozzeria'));
            elencoMilestone.add(createMilestone('Consegna Ricambi', '2024-12-08', 'P', 'pending', 'In attesa ricambi carrozzeria'));
            elencoMilestone.add(createMilestone('Avvio Lavori', '2024-12-10', 'S', 'scheduled', 'Inizio lavori di carrozzeria previsto'));
            elencoMilestone.add(createMilestone('Auto Pronta', '2024-12-20', 'S', 'scheduled', 'Completamento riparazione previsto'));
            elencoMilestone.add(createMilestone('Auto Restituita', '2024-12-21', 'S', 'scheduled', 'Restituzione veicolo prevista'));
        } else if (tipoIncarico == 'MEC') {
            // Milestone specifiche per meccanica
            elencoMilestone.add(createMilestone('Creazione Incarico da Officina', '2024-12-01', 'C', 'completed', 'Incarico meccanica creato'));
            elencoMilestone.add(createMilestone('Check-in', '2024-12-02', 'C', 'completed', 'Veicolo in officina meccanica'));
            elencoMilestone.add(createMilestone('Diagnosi Completata', '2024-12-03', 'C', 'completed', 'Diagnosi meccanica completata'));
            elencoMilestone.add(createMilestone('Preventivo Autorizzato', '2024-12-04', 'P', 'pending', 'Preventivo meccanica in approvazione'));
            elencoMilestone.add(createMilestone('Avvio Lavori', '2024-12-06', 'S', 'scheduled', 'Inizio riparazione meccanica'));
            elencoMilestone.add(createMilestone('Auto Pronta', '2024-12-08', 'S', 'scheduled', 'Completamento riparazione'));
        } else if (tipoIncarico == 'CRI') {
            // Milestone specifiche per cristalli
            elencoMilestone.add(createMilestone('Creazione Incarico da Officina', '2024-12-03', 'C', 'completed', 'Incarico cristalli creato'));
            elencoMilestone.add(createMilestone('Check-in', '2024-12-03', 'C', 'completed', 'Veicolo presso cristalleria'));
            elencoMilestone.add(createMilestone('Preventivo Autorizzato', '2024-12-03', 'C', 'completed', 'Preventivo cristalli approvato'));
            elencoMilestone.add(createMilestone('Sostituzione Completata', '2024-12-03', 'C', 'completed', 'Cristallo sostituito'));
            elencoMilestone.add(createMilestone('Auto Restituita', '2024-12-03', 'C', 'completed', 'Veicolo riconsegnato'));
        }
        
        response.put('elencoMilestone', elencoMilestone);
        return JSON.serialize(response);
    }
    
    /**
     * Helper method per creare una milestone
     */
    private Map<String, Object> createMilestone(String descrizione, String data, String codStato, String descStato, String descAggiuntiva) {
        Map<String, Object> milestone = new Map<String, Object>();
        milestone.put('descrizione', descrizione);
        milestone.put('dataMilestone', data);
        milestone.put('codStato', codStato);
        milestone.put('descrizioneStato', descStato);
        milestone.put('descrizioneAggiuntiva', descAggiuntiva);
        return milestone;
    }
    
    /**
     * Metodo statico per ottenere una risposta mock specifica per i test
     */
    public static String getMockElencoMilestone() {
        urcs_TimelineIncaricoMock mock = new urcs_TimelineIncaricoMock();
        return mock.generateElencoMilestoneResponse();
    }
    
    /**
     * Metodo statico per ottenere milestone per tipo incarico specifico
     */
    public static String getMockMilestoneByTipo(String tipoIncarico) {
        urcs_TimelineIncaricoMock mock = new urcs_TimelineIncaricoMock();
        return mock.generateMilestoneByTipoIncarico(tipoIncarico);
    }
}
