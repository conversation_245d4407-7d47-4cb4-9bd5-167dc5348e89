/**
 * @File Name         : urcs_IP_GetElencoLavorazioni.cls
 * @Description       : Integration Procedure per recuperare l'elenco delle lavorazioni Unipol
 * <AUTHOR> VE
 * @Group             : 
 * @Last Modified On  : 29-09-2025
 * @Last Modified By  : VE
 * @cicd_tests urcs_IP_GetElencoLavorazioni_Test
**/
global with sharing class urcs_IP_GetElencoLavorazioni implements System.Callable {

    public Object call(String action, Map<String,Object> args){
        Map<String,Object> input = (Map<String,Object>) args.get('input');
        Map<String,Object> output = (Map<String,Object>) args.get('output');
        Map<String,Object> options = (Map<String,Object>) args.get('options');
        invokeMethod(input, output, options);
        return null;
    }
        
    @AuraEnabled
    global static Map<String,Object> invokeMethod(Map<String,Object> input,Map<String,Object> output, Map<String,Object> options){
        
        system.debug('input '+ JSON.serialize(input));
        system.debug('output '+ JSON.serialize(output));
        system.debug('options '+ JSON.serialize(options));
        
        Map<String,Object> setValueMap = (Map<String,Object>) input.get('Set Values');
        String method = (String) setValueMap.get('methodExecute');
        
        try{
            if(method.equalsIgnoreCase('init')){
                output = getElencoLavorazioni(input, output, options);
            }
        }catch(Exception e){
            output.put('error', e.getMessage() + ' ' + e.getStackTraceString());
            System.debug('Error in urcs_IP_GetElencoLavorazioni: ' + e.getMessage());
        }
        return output;
    }
    
    /**
     * Metodo principale per recuperare l'elenco delle lavorazioni
     */
    public static Map<String,Object> getElencoLavorazioni(Map<String,Object> input, Map<String,Object> output, Map<String,Object> options){
        try{
            String recordId = null != (String)input.get('recordId') ? (String)input.get('recordId') : null;
            String vehicleId = null != (String)input.get('vehicleId') ? (String)input.get('vehicleId') : null;
            String contractId = null != (String)input.get('contractId') ? (String)input.get('contractId') : null;
            
            system.debug('recordId: ' + recordId);
            system.debug('vehicleId: ' + vehicleId);
            system.debug('contractId: ' + contractId);
            
            // In ambiente di test, usa il mock
            if(Test.isRunningTest()) {
                String mockResponse = urcs_InterventiUnipolServiceMock.getMockElencoLavorazioni();
                Map<String, Object> responseData = (Map<String, Object>) JSON.deserializeUntyped(mockResponse);
                
                output.put('elencoLavorazioni', responseData.get('elencoLavorazioni'));
                output.put('success', true);
                output.put('message', 'Dati recuperati con successo (mock)');
                
                system.debug('Mock response: ' + mockResponse);
                
            } else {
                // In ambiente reale, qui faresti la chiamata al servizio esterno
                // Per ora restituiamo il mock anche in produzione per testing
                String mockResponse = urcs_InterventiUnipolServiceMock.getMockElencoLavorazioni();
                Map<String, Object> responseData = (Map<String, Object>) JSON.deserializeUntyped(mockResponse);
                
                output.put('elencoLavorazioni', responseData.get('elencoLavorazioni'));
                output.put('success', true);
                output.put('message', 'Dati recuperati con successo');
                
                // TODO: Implementare la chiamata reale al servizio Unipol
                // HttpRequest req = new HttpRequest();
                // req.setEndpoint('https://api.unipol.it/interventi/elenco');
                // req.setMethod('GET');
                // req.setHeader('Content-Type', 'application/json');
                // 
                // // Aggiungi parametri di ricerca
                // if(vehicleId != null) {
                //     req.setEndpoint(req.getEndpoint() + '?vehicleId=' + vehicleId);
                // }
                // 
                // Http http = new Http();
                // HttpResponse res = http.send(req);
                // 
                // if(res.getStatusCode() == 200) {
                //     Map<String, Object> responseData = (Map<String, Object>) JSON.deserializeUntyped(res.getBody());
                //     output.put('elencoLavorazioni', responseData.get('elencoLavorazioni'));
                //     output.put('success', true);
                // } else {
                //     output.put('error', 'Errore nella chiamata al servizio: ' + res.getStatus());
                //     output.put('success', false);
                // }
            }
            
        } catch (Exception e){
            output.put('elencoLavorazioni', null);
            output.put('success', false);
            output.put('error', e.getMessage());
            System.debug('Error in urcs_IP_GetElencoLavorazioni method getElencoLavorazioni: '+ json.serializePretty(e));
        }

        return output;
    }
    
    /**
     * Metodo helper per validare i parametri di input
     */
    private static Boolean validateInput(Map<String,Object> input) {
        String recordId = (String)input.get('recordId');
        String vehicleId = (String)input.get('vehicleId');
        String contractId = (String)input.get('contractId');
        
        // Almeno uno dei parametri deve essere presente
        return (recordId != null && recordId != '') || 
               (vehicleId != null && vehicleId != '') || 
               (contractId != null && contractId != '');
    }
    
    /**
     * Metodo per filtrare le lavorazioni in base ai criteri
     */
    private static List<Object> filterLavorazioni(List<Object> lavorazioni, Map<String,Object> filters) {
        if(filters == null || filters.isEmpty()) {
            return lavorazioni;
        }
        
        List<Object> filteredList = new List<Object>();
        
        for(Object lavorazioneObj : lavorazioni) {
            Map<String, Object> lavorazione = (Map<String, Object>) lavorazioneObj;
            Boolean matchesFilter = true;
            
            // Filtro per tipo incarico
            if(filters.containsKey('tipoIncarico')) {
                String filterTipo = (String) filters.get('tipoIncarico');
                String lavorazioneTipo = (String) lavorazione.get('tipoIncarico');
                if(filterTipo != null && !filterTipo.equals(lavorazioneTipo)) {
                    matchesFilter = false;
                }
            }
            
            // Filtro per stato lavorazione
            if(filters.containsKey('statoLavorazione')) {
                String filterStato = (String) filters.get('statoLavorazione');
                String lavorazioneStato = (String) lavorazione.get('codStatoLavorazione');
                if(filterStato != null && !filterStato.equals(lavorazioneStato)) {
                    matchesFilter = false;
                }
            }
            
            if(matchesFilter) {
                filteredList.add(lavorazione);
            }
        }
        
        return filteredList;
    }
}
