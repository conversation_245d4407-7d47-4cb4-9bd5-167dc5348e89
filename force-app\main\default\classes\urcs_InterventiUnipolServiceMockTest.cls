/**
 * Test class per dimostrare l'utilizzo delle classi mockup
 * per le flexcard degli interventi Unipol
 */
@isTest
private class urcs_InterventiUnipolServiceMockTest {
    
    @isTest
    static void testElencoLavorazioniMock() {
        // Test del mock per l'elenco lavorazioni (parent/child flexcard)
        Test.startTest();
        
        // Imposta il mock per le chiamate HTTP
        Test.setMock(HttpCalloutMock.class, new urcs_InterventiUnipolServiceMock());
        
        // Simula una chiamata HTTP
        HttpRequest req = new HttpRequest();
        req.setEndpoint('https://example.com/api/interventi');
        req.setMethod('GET');
        
        Http http = new Http();
        HttpResponse res = http.send(req);
        
        Test.stopTest();
        
        // Verifica la risposta
        System.assertEquals(200, res.getStatusCode(), 'Status code should be 200');
        System.assertNotEquals(null, res.getBody(), 'Response body should not be null');
        
        // Deserializza e verifica il contenuto
        Map<String, Object> responseMap = (Map<String, Object>) JSON.deserializeUntyped(res.getBody());
        System.assert(responseMap.containsKey('elencoLavorazioni'), 'Response should contain elencoLavorazioni');
        
        List<Object> lavorazioni = (List<Object>) responseMap.get('elencoLavorazioni');
        System.assertEquals(5, lavorazioni.size(), 'Should have 5 lavorazioni');
        
        // Verifica il primo elemento
        Map<String, Object> primaLavorazione = (Map<String, Object>) lavorazioni[0];
        System.assertEquals('FA213BB', primaLavorazione.get('targa'), 'First lavorazione should have correct targa');
        System.assertEquals('Mario Rossi', primaLavorazione.get('danneggiato'), 'First lavorazione should have correct danneggiato');
        System.assertEquals('CAR', primaLavorazione.get('tipoIncarico'), 'First lavorazione should be CAR type');
    }
    
    @isTest
    static void testTimelineMilestonesMock() {
        // Test del mock per le milestone della timeline
        Test.startTest();
        
        // Imposta il mock per le chiamate HTTP
        Test.setMock(HttpCalloutMock.class, new urcs_TimelineIncaricoMock());
        
        // Simula una chiamata HTTP
        HttpRequest req = new HttpRequest();
        req.setEndpoint('https://example.com/api/timeline');
        req.setMethod('GET');
        
        Http http = new Http();
        HttpResponse res = http.send(req);
        
        Test.stopTest();
        
        // Verifica la risposta
        System.assertEquals(200, res.getStatusCode(), 'Status code should be 200');
        System.assertNotEquals(null, res.getBody(), 'Response body should not be null');
        
        // Deserializza e verifica il contenuto
        Map<String, Object> responseMap = (Map<String, Object>) JSON.deserializeUntyped(res.getBody());
        System.assert(responseMap.containsKey('elencoMilestone'), 'Response should contain elencoMilestone');
        
        List<Object> milestones = (List<Object>) responseMap.get('elencoMilestone');
        System.assertEquals(7, milestones.size(), 'Should have 7 milestones');
        
        // Verifica la prima milestone
        Map<String, Object> primaMilestone = (Map<String, Object>) milestones[0];
        System.assertEquals('Creazione Incarico da Officina', primaMilestone.get('descrizione'), 'First milestone should have correct description');
        System.assertEquals('C', primaMilestone.get('codStato'), 'First milestone should be completed');
    }
    
    @isTest
    static void testStaticMethods() {
        // Test dei metodi statici per ottenere dati mock
        Test.startTest();
        
        String elencoLavorazioni = urcs_InterventiUnipolServiceMock.getMockElencoLavorazioni();
        String elencoMilestone = urcs_TimelineIncaricoMock.getMockElencoMilestone();
        
        Test.stopTest();
        
        // Verifica che i metodi statici restituiscano dati validi
        System.assertNotEquals(null, elencoLavorazioni, 'Static method should return valid data');
        System.assertNotEquals(null, elencoMilestone, 'Static method should return valid data');
        
        // Verifica che siano JSON validi
        Map<String, Object> lavorazioniMap = (Map<String, Object>) JSON.deserializeUntyped(elencoLavorazioni);
        Map<String, Object> milestoneMap = (Map<String, Object>) JSON.deserializeUntyped(elencoMilestone);
        
        System.assert(lavorazioniMap.containsKey('elencoLavorazioni'), 'Should contain elencoLavorazioni key');
        System.assert(milestoneMap.containsKey('elencoMilestone'), 'Should contain elencoMilestone key');
    }
    
    @isTest
    static void testMilestoneByTipoIncarico() {
        // Test delle milestone specifiche per tipo incarico
        Test.startTest();
        
        String milestoneCAR = urcs_TimelineIncaricoMock.getMockMilestoneByTipo('CAR');
        String milestoneMEC = urcs_TimelineIncaricoMock.getMockMilestoneByTipo('MEC');
        String milestoneCRI = urcs_TimelineIncaricoMock.getMockMilestoneByTipo('CRI');
        
        Test.stopTest();
        
        // Verifica che ogni tipo abbia milestone specifiche
        Map<String, Object> carMap = (Map<String, Object>) JSON.deserializeUntyped(milestoneCAR);
        Map<String, Object> mecMap = (Map<String, Object>) JSON.deserializeUntyped(milestoneMEC);
        Map<String, Object> criMap = (Map<String, Object>) JSON.deserializeUntyped(milestoneCRI);
        
        List<Object> carMilestones = (List<Object>) carMap.get('elencoMilestone');
        List<Object> mecMilestones = (List<Object>) mecMap.get('elencoMilestone');
        List<Object> criMilestones = (List<Object>) criMap.get('elencoMilestone');
        
        System.assertEquals(7, carMilestones.size(), 'CAR should have 7 milestones');
        System.assertEquals(6, mecMilestones.size(), 'MEC should have 6 milestones');
        System.assertEquals(5, criMilestones.size(), 'CRI should have 5 milestones');
        
        // Verifica milestone specifiche per carrozzeria
        Map<String, Object> carFirstMilestone = (Map<String, Object>) carMilestones[0];
        System.assertEquals('Creazione Incarico da Officina', carFirstMilestone.get('descrizione'));
        System.assert(((String) carFirstMilestone.get('descrizioneAggiuntiva')).contains('carrozzeria'));
    }
}
